<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;22eed058-2667-47c3-bfaf-3403fc49b806&quot;,&quot;conversations&quot;:{&quot;0f29c4d9-9710-4012-9bfd-b6f686cb0bc1&quot;:{&quot;id&quot;:&quot;0f29c4d9-9710-4012-9bfd-b6f686cb0bc1&quot;,&quot;createdAtIso&quot;:&quot;2025-06-16T09:54:48.060Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-16T09:54:48.060Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;7c7ccab0-ec4e-4b42-b26c-14b578fadc5c&quot;},&quot;22eed058-2667-47c3-bfaf-3403fc49b806&quot;:{&quot;id&quot;:&quot;22eed058-2667-47c3-bfaf-3403fc49b806&quot;,&quot;createdAtIso&quot;:&quot;2025-06-16T09:54:48.683Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-16T09:54:48.683Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;6ce4b682-6e0c-47cd-8718-20d8657255b6&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>