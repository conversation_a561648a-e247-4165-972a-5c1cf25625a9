<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="0a815b88-ba5b-446e-9a4e-4f0da7a7a957" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/training.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/consigne.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/database.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/init_db.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/main.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/models.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/requirements.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/schemas.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_main.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2yaKSse1SN8pwoza338NlfcKgug" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.keymap",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0a815b88-ba5b-446e-9a4e-4f0da7a7a957" name="Changes" comment="" />
      <created>1750067246214</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750067246214</updated>
      <workItem from="1750067247275" duration="100000" />
      <workItem from="1750067411406" duration="79000" />
      <workItem from="1750067508148" duration="91000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>