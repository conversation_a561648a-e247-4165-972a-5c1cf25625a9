# 📋 BACKLOG - API Bibliothèque

**Sprint Duration:** 3 jours  
**Total Points:** 20  
**Objectif:** Finaliser l'API de gestion de bibliothèque

---

## 🐛 **BUGS À CORRIGER**

### **BUG-001** - Correction du retour de livre *(2 points)*
**En tant qu'** utilisateur de l'API  
**Je veux** pouvoir marquer un livre comme retourné correctement  
**Afin que** le système reflète l'état réel des emprunts  

**Critères d'acceptation :**
- [ ] La route `PUT /loans/{loan_id}/return` met à jour `is_returned = True`
- [ ] La route met à jour `returned_date` avec la date actuelle
- [ ] Le livre redevient disponible (`available = True`)
- [ ] Une erreur 404 est retournée si l'emprunt n'existe pas
- [ ] Une erreur 400 est retournée si le livre est déjà retourné
- [ ] Tests unitaires couvrant tous les cas

---

## 🔧 **FONCTIONNALITÉS À TERMINER**

### **FEAT-001** - Création de livres *(3 points)*
**En tant qu'** bibliothécaire  
**Je veux** pouvoir ajouter de nouveaux livres dans le système  
**Afin d'** enrichir le catalogue de la bibliothèque  

**Critères d'acceptation :**
- [ ] La route `POST /books/` crée un nouveau livre
- [ ] Validation que l'ISBN est unique (erreur 400 si doublon)
- [ ] Validation que l'auteur existe (erreur 404 si inexistant)
- [ ] Le livre est créé avec `available = True` par défaut
- [ ] Retour du livre créé avec status 201
- [ ] Tests unitaires pour tous les cas (succès, ISBN doublon, auteur inexistant)

### **FEAT-002** - Système d'emprunt complet *(5 points)*
**En tant qu'** utilisateur  
**Je veux** pouvoir emprunter des livres disponibles  
**Afin de** les lire à domicile  

**Critères d'acceptation :**
- [ ] La route `POST /loans/` crée un nouvel emprunt
- [ ] Vérification que le livre est disponible (erreur 400 si indisponible)
- [ ] Vérification que l'utilisateur n'a pas déjà 3 emprunts actifs (erreur 400)
- [ ] Calcul automatique de `due_date` = `loan_date` + 14 jours
- [ ] Mise à jour du livre : `available = False`
- [ ] Retour de l'emprunt créé avec status 201
- [ ] Tests unitaires couvrant toutes les règles métier

---

## 🆕 **NOUVELLES FONCTIONNALITÉS**

### **FEAT-003** - Gestion des auteurs *(2 points)*
**En tant qu'** bibliothécaire  
**Je veux** pouvoir ajouter de nouveaux auteurs  
**Afin de** pouvoir ensuite leur associer des livres  

**Critères d'acceptation :**
- [ ] La route `POST /authors/` crée un nouvel auteur
- [ ] Validation des données obligatoires (nom requis)
- [ ] Gestion des données optionnelles (date de naissance, nationalité, biographie)
- [ ] Retour de l'auteur créé avec status 201
- [ ] Tests unitaires pour la création d'auteur

### **FEAT-004** - Consultation des livres par auteur *(2 points)*
**En tant qu'** utilisateur  
**Je veux** voir tous les livres d'un auteur spécifique  
**Afin de** découvrir son œuvre complète  

**Critères d'acceptation :**
- [ ] La route `GET /authors/{author_id}/books` retourne la liste des livres
- [ ] Erreur 404 si l'auteur n'existe pas
- [ ] Liste vide si l'auteur n'a pas de livres
- [ ] Informations complètes sur chaque livre (avec détails auteur)
- [ ] Tests unitaires pour tous les cas

---

## 🧪 **TESTS ET QUALITÉ**

### **TEST-001** - Tests unitaires de logique pure *(2 points)*
**En tant que** développeur
**Je veux** tester la logique métier sans dépendances externes
**Afin de** valider les algorithmes de façon isolée

**Critères d'acceptation :**
- [ ] Fonction `can_user_borrow_book()` testée (règle des 3 emprunts max)
- [ ] Fonction `calculate_due_date()` testée (calcul +14 jours)
- [ ] Fonction `is_isbn_valid()` testée (validation format ISBN)
- [ ] Tests sans FastAPI, sans base de données, sans HTTP
- [ ] Couverture de tous les cas limites et erreurs
- [ ] Tests rapides (< 1ms chacun)

### **TEST-002** - Tests unitaires de routes avec mocks *(2 points)*
**En tant que** développeur
**Je veux** tester les routes en isolant la base de données
**Afin de** valider la logique des endpoints sans effets de bord

**Critères d'acceptation :**
- [ ] Tests des routes avec `mocker` (pytest-mock)
- [ ] Mock des appels à la base de données
- [ ] Validation des status codes et réponses JSON
- [ ] Tests des cas d'erreur (404, 400, 422)
- [ ] Isolation complète des tests (pas de vraie BDD)
- [ ] Vérification des appels aux mocks

### **TEST-003** - Tests d'intégration complets *(2 points)*
**En tant que** développeur
**Je veux** tester le système complet end-to-end
**Afin de** valider le comportement réel de l'API

**Critères d'acceptation :**
- [ ] Tests avec vraie base de données de test (SQLite en mémoire)
- [ ] Vérification des données persistées en base
- [ ] Tests des scénarios complets (création → lecture → modification)
- [ ] Fixtures pour setup/teardown de la base de test
- [ ] Tests des règles métier complexes (workflow d'emprunt complet)
- [ ] Nettoyage automatique entre les tests

### **QUAL-001** - Refactoring et extraction de logique métier *(2 points)*
**En tant que** développeur
**Je veux** extraire la logique métier des routes
**Afin de** pouvoir la tester de façon unitaire et améliorer la maintenabilité

**Critères d'acceptation :**
- [ ] Création d'un module `business_logic.py` avec les fonctions pures
- [ ] Extraction de `can_user_borrow_book()` des routes
- [ ] Extraction de `calculate_due_date()` des routes
- [ ] Extraction de `is_isbn_valid()` des routes
- [ ] Routes qui utilisent ces fonctions extraites
- [ ] Nommage explicite et documentation des fonctions
- [ ] Respect des conventions Python (PEP 8)

---

## 📊 **RÉPARTITION DES POINTS**

| Catégorie | Points | Pourcentage |
|-----------|--------|-------------|
| Bugs | 2 | 10% |
| Fonctionnalités à terminer | 8 | 40% |
| Nouvelles fonctionnalités | 4 | 20% |
| Tests unitaires purs | 2 | 10% |
| Tests unitaires avec mocks | 2 | 10% |
| Tests d'intégration | 2 | 10% |
| Refactoring et qualité | 2 | 10% |
| **TOTAL** | **20** | **100%** |

---

## 🎯 **PRIORITÉS RECOMMANDÉES**

1. **QUAL-001** - Refactoring (extraction de logique métier)
2. **TEST-001** - Tests unitaires purs (sur la logique extraite)
3. **BUG-001** - Correction critique du retour de livre
4. **FEAT-001** - Création de livres (base du système)
5. **FEAT-003** - Gestion des auteurs (prérequis pour les livres)
6. **TEST-002** - Tests unitaires avec mocks (routes isolées)
7. **FEAT-002** - Système d'emprunt (fonctionnalité complexe)
8. **TEST-003** - Tests d'intégration (système complet)
9. **FEAT-004** - Livres par auteur (fonctionnalité bonus)

---

## ⚠️ **NOTES IMPORTANTES**

- **Approche TDD recommandée** : Refactoring → Tests purs → Tests mocks → Tests intégration
- **Les 3 types de tests sont obligatoires** selon les consignes de l'épreuve
- **Tests unitaires purs = priorité absolue** (logique métier isolée)
- **Chaque fonctionnalité doit être testée** avec les 3 approches quand applicable
- **Commits réguliers** avec messages explicites
- **Documentation des choix techniques** dans les commentaires

## 📚 **RAPPEL DES 3 TYPES DE TESTS**

1. **Tests unitaires purs** : Logique métier sans HTTP/BDD
2. **Tests unitaires avec mocks** : Routes FastAPI avec BDD mockée
3. **Tests d'intégration** : Routes complètes avec vraie BDD de test
