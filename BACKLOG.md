# 📋 BACKLOG - API Bibliothèque

**Sprint Duration:** 3 jours  
**Total Points:** 20  
**Objectif:** Finaliser l'API de gestion de bibliothèque

---

## 🐛 **BUGS À CORRIGER**

### **BUG-001** - Correction du retour de livre *(2 points)*
**En tant qu'** utilisateur de l'API  
**Je veux** pouvoir marquer un livre comme retourné correctement  
**Afin que** le système reflète l'état réel des emprunts  

**Critères d'acceptation :**
- [ ] La route `PUT /loans/{loan_id}/return` met à jour `is_returned = True`
- [ ] La route met à jour `returned_date` avec la date actuelle
- [ ] Le livre redevient disponible (`available = True`)
- [ ] Une erreur 404 est retournée si l'emprunt n'existe pas
- [ ] Une erreur 400 est retournée si le livre est déjà retourné
- [ ] Tests unitaires couvrant tous les cas

---

## 🔧 **FONCTIONNALITÉS À TERMINER**

### **FEAT-001** - Création de livres *(3 points)*
**En tant qu'** bibliothécaire  
**Je veux** pouvoir ajouter de nouveaux livres dans le système  
**Afin d'** enrichir le catalogue de la bibliothèque  

**Critères d'acceptation :**
- [ ] La route `POST /books/` crée un nouveau livre
- [ ] Validation que l'ISBN est unique (erreur 400 si doublon)
- [ ] Validation que l'auteur existe (erreur 404 si inexistant)
- [ ] Le livre est créé avec `available = True` par défaut
- [ ] Retour du livre créé avec status 201
- [ ] Tests unitaires pour tous les cas (succès, ISBN doublon, auteur inexistant)

### **FEAT-002** - Système d'emprunt complet *(5 points)*
**En tant qu'** utilisateur  
**Je veux** pouvoir emprunter des livres disponibles  
**Afin de** les lire à domicile  

**Critères d'acceptation :**
- [ ] La route `POST /loans/` crée un nouvel emprunt
- [ ] Vérification que le livre est disponible (erreur 400 si indisponible)
- [ ] Vérification que l'utilisateur n'a pas déjà 3 emprunts actifs (erreur 400)
- [ ] Calcul automatique de `due_date` = `loan_date` + 14 jours
- [ ] Mise à jour du livre : `available = False`
- [ ] Retour de l'emprunt créé avec status 201
- [ ] Tests unitaires couvrant toutes les règles métier

---

## 🆕 **NOUVELLES FONCTIONNALITÉS**

### **FEAT-003** - Gestion des auteurs *(2 points)*
**En tant qu'** bibliothécaire  
**Je veux** pouvoir ajouter de nouveaux auteurs  
**Afin de** pouvoir ensuite leur associer des livres  

**Critères d'acceptation :**
- [ ] La route `POST /authors/` crée un nouvel auteur
- [ ] Validation des données obligatoires (nom requis)
- [ ] Gestion des données optionnelles (date de naissance, nationalité, biographie)
- [ ] Retour de l'auteur créé avec status 201
- [ ] Tests unitaires pour la création d'auteur

### **FEAT-004** - Consultation des livres par auteur *(2 points)*
**En tant qu'** utilisateur  
**Je veux** voir tous les livres d'un auteur spécifique  
**Afin de** découvrir son œuvre complète  

**Critères d'acceptation :**
- [ ] La route `GET /authors/{author_id}/books` retourne la liste des livres
- [ ] Erreur 404 si l'auteur n'existe pas
- [ ] Liste vide si l'auteur n'a pas de livres
- [ ] Informations complètes sur chaque livre (avec détails auteur)
- [ ] Tests unitaires pour tous les cas

---

## 🧪 **TESTS ET QUALITÉ**

### **TEST-001** - Couverture de tests complète *(4 points)*
**En tant que** développeur  
**Je veux** avoir une suite de tests complète  
**Afin de** garantir la fiabilité de l'API  

**Critères d'acceptation :**
- [ ] Tests unitaires pour toutes les routes implémentées
- [ ] Tests des cas d'erreur (404, 400, etc.)
- [ ] Tests des règles métier complexes (limites d'emprunt)
- [ ] Utilisation de fixtures pour les données de test
- [ ] Isolation des tests (base de données de test)
- [ ] Tous les tests passent avec `pytest -v`

### **QUAL-001** - Amélioration de la qualité du code *(2 points)*
**En tant que** développeur  
**Je veux** un code maintenable et bien structuré  
**Afin de** faciliter les évolutions futures  

**Critères d'acceptation :**
- [ ] Fonctions avec une responsabilité unique
- [ ] Nommage explicite des variables et fonctions
- [ ] Commentaires pertinents sur la logique métier
- [ ] Gestion d'erreurs cohérente
- [ ] Respect des conventions Python (PEP 8)
- [ ] Refactoring si nécessaire pour améliorer la testabilité

---

## 📊 **RÉPARTITION DES POINTS**

| Catégorie | Points | Pourcentage |
|-----------|--------|-------------|
| Bugs | 2 | 10% |
| Fonctionnalités à terminer | 8 | 40% |
| Nouvelles fonctionnalités | 4 | 20% |
| Tests et qualité | 6 | 30% |
| **TOTAL** | **20** | **100%** |

---

## 🎯 **PRIORITÉS RECOMMANDÉES**

1. **BUG-001** - Correction critique du retour de livre
2. **FEAT-001** - Création de livres (base du système)
3. **FEAT-003** - Gestion des auteurs (prérequis pour les livres)
4. **TEST-001** - Tests unitaires (critère principal d'évaluation)
5. **FEAT-002** - Système d'emprunt (fonctionnalité complexe)
6. **FEAT-004** - Livres par auteur (fonctionnalité bonus)
7. **QUAL-001** - Qualité du code (amélioration continue)

---

## ⚠️ **NOTES IMPORTANTES**

- **Les tests unitaires sont prioritaires** - ils représentent la majorité de la note
- **Chaque fonctionnalité doit être testée** avant d'être considérée comme terminée
- **Commits réguliers** avec messages explicites
- **Documentation des choix techniques** dans les commentaires
