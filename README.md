# 📚 API Bibliothèque - EC Test L3

## 🎯 Objectif de l'épreuve

Vous devez finaliser le développement de cette API de gestion de bibliothèque en FastAPI. Le projet contient des fonctionnalités partiellement implémentées, des bugs à corriger, et des tests à écrire.

## 🚀 Installation et démarrage

1. Installer les dépendances :
```bash
pip install -r requirements.txt
```

2. Initialiser la base de données :
```bash
python init_db.py
```

3. Démarrer l'application :
```bash
uvicorn main:app --reload
```

4. Accéder à la documentation : http://localhost:8000/docs

## 🧪 Tests

Exécuter les tests :
```bash
pytest test_main.py -v
```

## 📋 Fonctionnalités à implémenter

### ✅ Déjà implémenté
- Modèles de base de données
- Schemas Pydantic
- Route de base (`/`)
- Route de récupération des livres (partielle)

### 🔧 À terminer
- **Création de livres** (`POST /books/`) - Vérifier unicité ISBN
- **Création d'emprunts** (`POST /loans/`) - Implémenter règles métier
- **Tests unitaires** - Compléter tous les tests manquants

### 🆕 À implémenter entièrement
- **Création d'auteurs** (`POST /authors/`)
- **Livres par auteur** (`GET /authors/{author_id}/books`)

### 🐛 Bugs à corriger
- **Récupération d'un livre** (`GET /books/{book_id}`) - Bug dans la gestion d'erreur
- **Retour de livre** (`PUT /loans/{loan_id}/return`) - Logique incorrecte

## 📊 Règles métier importantes

### Emprunts
- Un livre ne peut être emprunté que s'il est disponible
- Un utilisateur ne peut emprunter plus de 3 livres simultanément
- Durée d'emprunt : 14 jours automatiquement
- Lors du retour : marquer `is_returned=True` et `returned_date=maintenant`

### Livres
- L'ISBN doit être unique
- Un livre emprunté doit avoir `available=False`

## 🎯 Critères d'évaluation

1. **Tests unitaires** (priorité maximale)
2. **Fonctionnalités complètes et sans bugs**
3. **Qualité du code** (nommage, architecture)
4. **Qualité des commits**

## 📁 Structure du projet

```
├── main.py           # Routes FastAPI
├── models.py         # Modèles SQLAlchemy
├── schemas.py        # Schemas Pydantic
├── database.py       # Configuration BDD
├── test_main.py      # Tests unitaires
├── init_db.py        # Script d'initialisation
├── requirements.txt  # Dépendances
└── library.db        # Base de données SQLite
```

## ⚠️ Rappels importants

- **Interdiction d'utiliser des LLM** de manière évidente
- **Respecter le framework FastAPI** (pas de remplacement)
- **Prioriser les tests unitaires** pour la notation
- **Commits réguliers et explicites**

Bon courage ! 🍀
