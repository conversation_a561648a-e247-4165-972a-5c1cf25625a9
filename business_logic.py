"""
Logique métier pure pour l'API Bibliothèque
Ces fonctions sont testables unitairement sans dépendances externes
"""

from datetime import datetime, timedelta
import re


def can_user_borrow_book(user_active_loans_count: int, book_available: bool) -> bool:
    """
    Détermine si un utilisateur peut emprunter un livre
    
    Règles métier:
    - L'utilisateur ne peut pas avoir plus de 3 emprunts actifs
    - Le livre doit être disponible
    
    Args:
        user_active_loans_count: Nombre d'emprunts actifs de l'utilisateur
        book_available: True si le livre est disponible
        
    Returns:
        bool: True si l'emprunt est autorisé, False sinon
    """
    if user_active_loans_count >= 3:
        return False
    
    if not book_available:
        return False
        
    return True


def calculate_due_date(loan_date: datetime) -> datetime:
    """
    Calcule la date de retour d'un emprunt
    
    Règle métier: 14 jours après la date d'emprunt
    
    Args:
        loan_date: Date de l'emprunt
        
    Returns:
        datetime: Date de retour prévue
    """
    return loan_date + timedelta(days=14)


def is_isbn_valid(isbn: str) -> bool:
    """
    Valide le format d'un ISBN
    
    Accepte les formats:
    - ISBN-10: 10 chiffres (avec ou sans tirets)
    - ISBN-13: 13 chiffres (avec ou sans tirets)
    
    Args:
        isbn: Chaîne ISBN à valider
        
    Returns:
        bool: True si le format est valide, False sinon
    """
    if not isbn:
        return False
    
    # Supprimer les tirets et espaces
    clean_isbn = re.sub(r'[-\s]', '', isbn)
    
    # Vérifier que ce sont uniquement des chiffres
    if not clean_isbn.isdigit():
        return False
    
    # Vérifier la longueur (10 ou 13 chiffres)
    return len(clean_isbn) in [10, 13]


def get_borrow_error_message(user_active_loans_count: int, book_available: bool) -> str:
    """
    Retourne un message d'erreur explicite pour un emprunt refusé
    
    Args:
        user_active_loans_count: Nombre d'emprunts actifs de l'utilisateur
        book_available: True si le livre est disponible
        
    Returns:
        str: Message d'erreur explicite
    """
    if user_active_loans_count >= 3:
        return "L'utilisateur a déjà atteint la limite de 3 emprunts simultanés"
    
    if not book_available:
        return "Ce livre n'est pas disponible pour l'emprunt"
    
    return "Emprunt autorisé"  # Ne devrait pas arriver si utilisé correctement
