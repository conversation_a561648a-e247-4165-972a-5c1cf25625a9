# EC Test L3

## Format de l'épreuve

L'épreuve se déroule sur 3 jours (sauf tiers-temps spécifique). Un PO est présent sur Discord le premier jour
pour distribuer le sujet, expliquer les attendus et vous aider pour le setup de vos machines.
Pour se rapprocher d'une situation réelle d'entreprise, vous allez découvrir et prendre en main un un projet
web existant en FastAPI. Il est strictement interdit de remplacer le framework existant.
Après avoir cloné le repo fourni par le PO, vous aurez un projet web démarré, avec un BDD à importer sur
votre machine de dev.
Au bout des 3 jours, vous devrez envoyer le lien de votre repo GitHub au PO qui a lancé l'épreuve. Ce repo doit
contenir votre version finale du code et de la BDD pour la correction (fichier sqlite inclu dans le repo). Le
GitHub sera préparé par le PO.

## Stack technique

L'objectif est de continuer et enrichir un projet web déjà démarré en Python. Vous serez évalués sur vos
compétences en développement :
API REST
Algorithmie
Tests unitaires et intégration

## Critères d'évaluation

Les critères d’évaluations sont :
Des fonctionnalités démarrées que vous devez terminer
Des fonctionnalités à réaliser entièrement
Des bugs à corriger
Sur chaque fonctionnalité, il y aura des tests unitaires à écrire ou compléter. Ce sont les tests unitaires qui
détermineront la majorité de votre note. Certaines fonctionnalités impliqueront de faire du refacto de code
pour pouvoir écrire correctement le test. Cette démarche est activement encouragée.
Chaque fonctionnalité est pondérée par sa complexité. Il y a également une partie sur la qualité du projet :
Qualité du code (nommage des variables, fonctions, classes, etc.)
Qualité des commentaires
Qualité de l'architecture
Qualité des commits

## Perte de points

Toute utilisation "évidente" de LLM (Copilot, ChatGPT, Claude, etc) implique un 0 à la fonctionnalité demandée.
Le but n'est pas de vous empêcher de les utiliser, mais c'est votre capacité de dev individuelle qui est évaluée,
pas celle d'un LLM.
En cas de retard, un malus de 0,5 point par tranche de 15 mins de retard sera appliquée sur la note finale
(ramenée sur 20 points).

## Conseils de préparation

Pour vous entraîné, faites un back en FastAPI, tout en faisant des tests. Sont conseillés (dans l'ordre de
priorité) :
Des tests unitaires sur la partie logique : algorithme pur, sans trace de requête HTTP
Des tests unitaires sur des routes en faisant du mock de base de données ou équivalent
Des tests d'intégrations vérifiant des routes complètes
