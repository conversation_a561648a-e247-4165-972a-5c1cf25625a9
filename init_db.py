"""
Script d'initialisation de la base de données avec des données de test
À exécuter une seule fois pour préparer la base de données
"""

from sqlalchemy.orm import sessionmaker
from database import engine
import models
from datetime import datetime, timedelta

# Créer les tables
models.Base.metadata.create_all(bind=engine)

# Créer une session
SessionLocal = sessionmaker(bind=engine)
db = SessionLocal()

try:
    # Créer quelques auteurs
    author1 = models.Author(
        name="<PERSON>",
        birth_date=datetime(1802, 2, 26),
        nationality="Française",
        biography="<PERSON><PERSON><PERSON><PERSON><PERSON>, dramaturge, poète et homme politique français."
    )
    
    author2 = models.Author(
        name="<PERSON>",
        birth_date=datetime(1890, 9, 15),
        nationality="Britannique",
        biography="Femme de lettres britannique, auteure de romans policiers."
    )
    
    db.add(author1)
    db.add(author2)
    db.commit()
    
    # Créer quelques livres
    book1 = models.Book(
        title="Les Misérables",
        isbn="9782070409228",
        publication_year=1862,
        pages=1232,
        author_id=author1.id,
        available=True
    )
    
    book2 = models.Book(
        title="Le Crime de l'Orient-Express",
        isbn="9782253006329",
        publication_year=1934,
        pages=256,
        author_id=author2.id,
        available=False  # Livre déjà emprunté
    )
    
    db.add(book1)
    db.add(book2)
    db.commit()
    
    # Créer quelques utilisateurs
    user1 = models.User(
        email="<EMAIL>",
        name="Alice Martin",
        registration_date=datetime.utcnow() - timedelta(days=30)
    )
    
    user2 = models.User(
        email="<EMAIL>",
        name="Bob Dupont",
        registration_date=datetime.utcnow() - timedelta(days=15)
    )
    
    db.add(user1)
    db.add(user2)
    db.commit()
    
    # Créer un emprunt en cours
    loan1 = models.Loan(
        user_id=user1.id,
        book_id=book2.id,
        loan_date=datetime.utcnow() - timedelta(days=5),
        due_date=datetime.utcnow() + timedelta(days=9),
        is_returned=False
    )
    
    db.add(loan1)
    db.commit()
    
    print("✅ Base de données initialisée avec succès !")
    print(f"📚 {db.query(models.Book).count()} livres créés")
    print(f"✍️ {db.query(models.Author).count()} auteurs créés")
    print(f"👥 {db.query(models.User).count()} utilisateurs créés")
    print(f"📖 {db.query(models.Loan).count()} emprunts créés")

except Exception as e:
    print(f"❌ Erreur lors de l'initialisation : {e}")
    db.rollback()

finally:
    db.close()
