from fastapi import FastAPI, HTTPException, Depends
from sqlalchemy.orm import Session
from database import get_db, engine
import models
import schemas
from typing import List

# Créer les tables
models.Base.metadata.create_all(bind=engine)

app = FastAPI(title="Bibliothèque API", version="1.0.0")


@app.get("/")
def read_root():
    return {"message": "API Bibliothèque - Version 1.0.0"}


# ==================== ROUTES LIVRES ====================


@app.post("/books/", response_model=schemas.Book)
def create_book(book: schemas.BookCreate, db: Session = Depends(get_db)):
    """Créer un nouveau livre - FONCTIONNALITÉ À TERMINER"""
    # TODO: Implémenter la création d'un livre
    # ATTENTION: Il faut vérifier que l'ISBN n'existe pas déjà
    pass


@app.get("/books/", response_model=List[schemas.Book])
def get_books(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Récupérer la liste des livres - FONCTIONNALITÉ PARTIELLEMENT IMPLÉMENTÉE"""
    books = db.query(models.Book).offset(skip).limit(limit).all()
    return books


@app.get("/books/{book_id}", response_model=schemas.Book)
def get_book(book_id: int, db: Session = Depends(get_db)):
    """Récupérer un livre par son ID - CONTIENT UN BUG"""
    book = db.query(models.Book).filter(models.Book.id == book_id).first()
    if book is None:
        raise HTTPException(status_code=404, detail="Livre non trouvé")
    return book


# ==================== ROUTES AUTEURS ====================


@app.post("/authors/", response_model=schemas.Author)
def create_author(author: schemas.AuthorCreate, db: Session = Depends(get_db)):
    """Créer un nouvel auteur - FONCTIONNALITÉ À IMPLÉMENTER ENTIÈREMENT"""
    # TODO: Implémenter complètement cette fonctionnalité
    pass


@app.get("/authors/{author_id}/books", response_model=List[schemas.Book])
def get_books_by_author(author_id: int, db: Session = Depends(get_db)):
    """Récupérer tous les livres d'un auteur - FONCTIONNALITÉ À IMPLÉMENTER"""
    # TODO: Implémenter cette fonctionnalité
    pass


# ==================== ROUTES EMPRUNTS ====================


@app.post("/loans/", response_model=schemas.Loan)
def create_loan(loan: schemas.LoanCreate, db: Session = Depends(get_db)):
    """Créer un nouvel emprunt - FONCTIONNALITÉ COMPLEXE À TERMINER"""
    # TODO: Implémenter la logique d'emprunt
    # RÈGLES MÉTIER:
    # - Un livre ne peut être emprunté que s'il est disponible
    # - Un utilisateur ne peut emprunter plus de 3 livres simultanément
    # - La date de retour doit être calculée automatiquement (14 jours)
    pass


@app.put("/loans/{loan_id}/return")
def return_book(loan_id: int, db: Session = Depends(get_db)):
    """Retourner un livre emprunté - CONTIENT PLUSIEURS BUGS"""
    loan = db.query(models.Loan).filter(models.Loan.id == loan_id).first()
    if not loan:
        raise HTTPException(status_code=404, detail="Emprunt non trouvé")

    # BUG: Cette logique est incorrecte
    loan.returned_date = None  # Devrait être la date actuelle
    loan.is_returned = False  # Devrait être True
    db.commit()
    return {"message": "Livre retourné avec succès"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
