from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Author(Base):
    __tablename__ = "authors"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    birth_date = Column(DateTime, nullable=True)
    nationality = Column(String(50), nullable=True)
    biography = Column(Text, nullable=True)
    
    # Relation avec les livres
    books = relationship("Book", back_populates="author")

class Book(Base):
    __tablename__ = "books"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    isbn = Column(String(13), unique=True, nullable=False)
    publication_year = Column(Integer, nullable=True)
    pages = Column(Integer, nullable=True)
    available = Column(Boolean, default=True)
    
    # Clé étrangère vers Author
    author_id = Column(Integer, ForeignKey("authors.id"))
    
    # Relations
    author = relationship("Author", back_populates="books")
    loans = relationship("Loan", back_populates="book")

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    registration_date = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relation avec les emprunts
    loans = relationship("Loan", back_populates="user")

class Loan(Base):
    __tablename__ = "loans"
    
    id = Column(Integer, primary_key=True, index=True)
    loan_date = Column(DateTime, default=datetime.utcnow)
    due_date = Column(DateTime, nullable=False)
    returned_date = Column(DateTime, nullable=True)
    is_returned = Column(Boolean, default=False)
    
    # Clés étrangères
    user_id = Column(Integer, ForeignKey("users.id"))
    book_id = Column(Integer, ForeignKey("books.id"))
    
    # Relations
    user = relationship("User", back_populates="loans")
    book = relationship("Book", back_populates="loans")
