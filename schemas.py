from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional

# ==================== SCHEMAS AUTEUR ====================


class AuthorBase(BaseModel):
    name: str
    birth_date: Optional[datetime] = None
    nationality: Optional[str] = None
    biography: Optional[str] = None


class AuthorCreate(AuthorBase):
    pass


class Author(AuthorBase):
    id: int

    class Config:
        from_attributes = True


# ==================== SCHEMAS LIVRE ====================


class BookBase(BaseModel):
    title: str
    isbn: str
    publication_year: Optional[int] = None
    pages: Optional[int] = None


class BookCreate(BookBase):
    author_id: int


class Book(BookBase):
    id: int
    available: bool
    author_id: int
    author: Optional[Author] = None

    class Config:
        from_attributes = True


# ==================== SCHEMAS UTILISATEUR ====================


class UserBase(BaseModel):
    email: EmailStr
    name: str


class UserCreate(UserBase):
    pass


class User(UserBase):
    id: int
    registration_date: datetime
    is_active: bool

    class Config:
        from_attributes = True


# ==================== SCHEMAS EMPRUNT ====================


class LoanBase(BaseModel):
    user_id: int
    book_id: int


class LoanCreate(LoanBase):
    pass


class Loan(LoanBase):
    id: int
    loan_date: datetime
    due_date: datetime
    returned_date: Optional[datetime] = None
    is_returned: bool
    user: Optional[User] = None
    book: Optional[Book] = None

    class Config:
        from_attributes = True
