import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database import get_db
from main import app
import models

# Base de données de test en mémoire
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

@pytest.fixture(scope="function")
def setup_database():
    """Fixture pour créer et nettoyer la base de données de test"""
    models.Base.metadata.create_all(bind=engine)
    yield
    models.Base.metadata.drop_all(bind=engine)

def test_read_root():
    """Test de la route racine - DÉJÀ IMPLÉMENTÉ"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "API Bibliothèque - Version 1.0.0"}

def test_create_book():
    """Test de création d'un livre - À IMPLÉMENTER"""
    # TODO: Écrire le test pour la création d'un livre
    # Ce test doit vérifier :
    # - Qu'un livre peut être créé avec des données valides
    # - Qu'une erreur est retournée si l'ISBN existe déjà
    # - Qu'une erreur est retournée si l'auteur n'existe pas
    pass

def test_get_books():
    """Test de récupération des livres - À COMPLÉTER"""
    # TODO: Compléter ce test
    response = client.get("/books/")
    assert response.status_code == 200
    # Ajouter d'autres assertions

def test_get_book_not_found():
    """Test de récupération d'un livre inexistant - À IMPLÉMENTER"""
    # TODO: Tester le cas où un livre n'existe pas
    pass

def test_create_author():
    """Test de création d'un auteur - À IMPLÉMENTER ENTIÈREMENT"""
    # TODO: Écrire complètement ce test
    pass

def test_create_loan():
    """Test de création d'un emprunt - TEST COMPLEXE À IMPLÉMENTER"""
    # TODO: Tester la logique d'emprunt avec toutes les règles métier
    # - Vérifier qu'un livre disponible peut être emprunté
    # - Vérifier qu'un livre indisponible ne peut pas être emprunté
    # - Vérifier la limite de 3 livres par utilisateur
    # - Vérifier le calcul automatique de la date de retour
    pass

def test_return_book():
    """Test de retour d'un livre - CONTIENT DES BUGS À CORRIGER"""
    # TODO: Ce test doit révéler les bugs dans la fonction return_book
    # et vérifier qu'ils sont corrigés
    pass
